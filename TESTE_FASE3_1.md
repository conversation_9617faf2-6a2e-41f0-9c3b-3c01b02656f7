# Testes da Fase 3.1: Gerenciamento Avançado e Conclusão de Tarefas
## 🎯 Objetivo
Este documento detalha o plano de testes para as funcionalidades pendentes e melhorias da Fase 3. O foco é validar a implementação das telas de gerenciamento de voluntários e acompanhamento de tarefas, o sistema de atribuição e conclusão colaborativa, e a criação dos componentes de UI necessários.

📋 Funcionalidades a Implementar e Testar
### 1. Tela Gerenciar Voluntários (ManageVolunteersScreen)
[ ] Lista de voluntários com cards informativos e indicador de disponibilidade.
[ ] Sistema de atribuição de microtasks com filtro de compatibilidade.
[ ] Controle de capacidade máxima (maxVolunteers) e remoção de voluntários.
[ ] Promoção de voluntários a gerenciadores.

### 2. Tela Acompanhar Tasks (TrackTasksScreen)
[ ] Visualização da lista de voluntários atribuídos em cada microtask.
[ ] Ações individuais (Iniciar/Concluir) e sistema de conclusão colaborativa.
[ ] Cálculo automático de progresso da Task pai.
[ ] Área de colaboração/notas por microtask.
[ ] Sistema de filtros (status, prioridade, responsável).
### 3. Componentes e Widgets
[ ] volunteer_card.dart, volunteer_list_widget.dart, skill_chip.dart.
[ ] Widgets genéricos como custom_app_bar.dart, error_widget.dart, confirmation_dialog.dart.